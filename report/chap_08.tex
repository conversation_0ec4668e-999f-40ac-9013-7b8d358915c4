\chapter{Application Setup and User Guide}
\label{chap:setup}

This chapter provides a comprehensive guide for setting up and running the complete Med4Solutions digital health platform. The system consists of multiple components that work together to provide a seamless experience for managing prescriptions and healthcare services.

\section{System Architecture Overview}

The Med4Solutions platform is composed of the following components:
\begin{itemize}
    \item \textbf{Backend API}: NestJS-based REST API server
    \item \textbf{Database}: MongoDB database for data persistence
    \item \textbf{Web Frontend}: Angular-based web application
    \item \textbf{Mobile Application}: Flutter-based iOS application
    \item \textbf{AI Processing Service}: Python-based OCR and prescription processing service
    \item \textbf{Company Distribution}: Pre-built backend and frontend distributions from Med4Solutions
\end{itemize}

\section{Prerequisites}

Before setting up the application, ensure the following software is installed on your system:

\begin{itemize}
    \item \textbf{Node.js} (version 18.x or higher)
    \item \textbf{MongoDB} (version 6.x or higher)
    \item \textbf{Python} (version 3.9 or higher)
    \item \textbf{Flutter SDK} (version 3.x or higher)
    \item \textbf{Angular CLI} (version 16.x or higher)
    \item \textbf{Git} for version control
\end{itemize}

\section{Database Setup}

\subsection{MongoDB Installation and Configuration}

1. Install MongoDB Community Edition following the official documentation for your operating system.

2. Start the MongoDB service:
\begin{terminalbox}
# On macOS with Homebrew
brew services start mongodb-community

# On Linux
sudo systemctl start mongod

# On Windows
net start MongoDB
\end{terminalbox}

3. Verify MongoDB is running by connecting to the default port:
\begin{terminalbox}
mongosh mongodb://localhost:27017
\end{terminalbox}

4. Create the application database:
\begin{terminalbox}
use med4solutions_db
\end{terminalbox}

\section{Backend Setup}

\subsection{Development Backend (NestJS)}

1. Navigate to the backend project directory:
\begin{terminalbox}
cd PFE_Backend
\end{terminalbox}

2. Install dependencies:
\begin{terminalbox}
npm install
\end{terminalbox}

3. Configure environment variables by creating a \texttt{.env} file:
\begin{terminalbox}
DATABASE_URL=mongodb://localhost:27017/med4solutions_db
JWT_SECRET=your_jwt_secret_key
PORT=3000
\end{terminalbox}

4. Start the development server:
\begin{terminalbox}
npm run start:dev
\end{terminalbox}

The backend API will be available at \texttt{http://localhost:3000}.

\subsection{Company Distribution Backend}

For the pre-built company distribution:

1. Navigate to the distribution directory:
\begin{terminalbox}
cd dists/backend
\end{terminalbox}

2. Run the distribution:
\begin{terminalbox}
node index.js
\end{terminalbox}

\textbf{Note}: The company distribution is a compiled version provided by Med4Solutions. The source code is not accessible, but it provides the same functionality as the development backend.

\section{AI Processing Service Setup}

The Python-based AI service handles OCR and prescription processing using Gemini API and Groq LLM.

1. Navigate to the AI service directory:
\begin{terminalbox}
cd PFE_AI
\end{terminalbox}

2. Install Python dependencies:
\begin{terminalbox}
pip install -r requirements.txt
\end{terminalbox}

3. Configure API keys in the environment or configuration file:
\begin{terminalbox}
GEMINI_API_KEY=your_gemini_api_key
GROQ_API_KEY=your_groq_api_key
\end{terminalbox}

4. Start the AI service:
\begin{terminalbox}
uvicorn main:app --host 0.0.0.0 --port 8001
\end{terminalbox}

The AI service will be available at \texttt{http://localhost:8001}.

\section{Frontend Setup}

\subsection{Development Frontend (Angular)}

1. Navigate to the frontend project directory:
\begin{terminalbox}
cd PFE_Frontend
\end{terminalbox}

2. Install dependencies:
\begin{terminalbox}
npm install
\end{terminalbox}

3. Configure the API endpoint in \texttt{src/environments/environment.ts}:
\begin{terminalbox}
export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000/api',
  aiServiceUrl: 'http://localhost:8001'
};
\end{terminalbox}

4. Start the development server:
\begin{terminalbox}
ng serve
\end{terminalbox}

The web application will be available at \texttt{http://localhost:4200}.

\subsection{Company Distribution Frontend}

For the pre-built company distribution:

1. Navigate to the distribution directory:
\begin{terminalbox}
cd dists/frontend
\end{terminalbox}

2. Install http-server globally if not already installed:
\begin{terminalbox}
npm install -g http-server
\end{terminalbox}

3. Run the distribution:
\begin{terminalbox}
http-server -p 4200
\end{terminalbox}

The web application will be available at \texttt{http://localhost:4200}.

\section{Mobile Application Setup}

\subsection{Flutter iOS Application}

1. Navigate to the mobile project directory:
\begin{verbatim}
cd PFE_Mobile
\end{verbatim}

2. Install Flutter dependencies:
\begin{verbatim}
flutter pub get
\end{verbatim}

3. Configure the API endpoints in \texttt{lib/config/api\_config.dart}:
\begin{verbatim}
class ApiConfig {
  static const String baseUrl = 'http://localhost:3000/api';
  static const String aiServiceUrl = 'http://localhost:8001';
}
\end{verbatim}

4. For iOS development, ensure Xcode is installed and configured.

5. Run the application on iOS simulator or device:
\begin{verbatim}
flutter run -d ios
\end{verbatim}

\section{Complete System Startup Sequence}

To run the complete system, follow this startup sequence:

\subsection{Step 1: Start Database}
\begin{verbatim}
# Start MongoDB service
brew services start mongodb-community  # macOS
# or
sudo systemctl start mongod  # Linux
\end{verbatim}

\subsection{Step 2: Start Backend Services}
\begin{verbatim}
# Terminal 1: Start main backend
cd PFE_Backend
npm run start:dev

# Terminal 2: Start AI service
cd PFE_AI
uvicorn main:app --host 0.0.0.0 --port 8001
\end{verbatim}

\subsection{Step 3: Start Frontend Applications}
\begin{verbatim}
# Terminal 3: Start web application
cd PFE_Frontend
ng serve

# Terminal 4: Start mobile application
cd PFE_Mobile
flutter run -d ios
\end{verbatim}

\subsection{Alternative: Using Company Distributions}
\begin{verbatim}
# Terminal 1: Start distributed backend
cd dists/backend
node index.js

# Terminal 2: Start AI service
cd PFE_AI
uvicorn main:app --host 0.0.0.0 --port 8001

# Terminal 3: Start distributed frontend
cd dists/frontend
http-server -p 4200

# Terminal 4: Start mobile application
cd PFE_Mobile
flutter run -d ios
\end{verbatim}

\section{System Verification}

After starting all components, verify the system is working correctly:

\begin{enumerate}
    \item \textbf{Backend API}: Visit \texttt{http://localhost:3000/api/health} to check API status
    \item \textbf{AI Service}: Visit \texttt{http://localhost:8001/docs} to access the FastAPI documentation
    \item \textbf{Web Application}: Open \texttt{http://localhost:4200} in a web browser
    \item \textbf{Mobile Application}: Check that the app launches successfully on the iOS simulator or device
    \item \textbf{Database}: Use MongoDB Compass or mongosh to verify database connectivity
\end{enumerate}

\section{Troubleshooting}

\subsection{Common Issues}

\textbf{Port Conflicts}: If ports 3000, 4200, or 8001 are already in use, modify the port configurations in the respective applications.

\textbf{Database Connection}: Ensure MongoDB is running and accessible. Check the connection string in the backend configuration.

\textbf{API Keys}: Verify that Gemini and Groq API keys are correctly configured for the AI service.

\textbf{Flutter Dependencies}: Run \texttt{flutter doctor} to check for any missing dependencies or configuration issues.

\textbf{CORS Issues}: If the frontend cannot connect to the backend, check CORS configuration in the NestJS application.

\section{User Guide}

\subsection{System Access}

The Med4Solutions platform provides different access levels:

\begin{itemize}
    \item \textbf{Patients}: Register and login through the mobile application or web interface
    \item \textbf{Pharmacists}: Login through the web interface with pharmacist credentials
    \item \textbf{Delivery Personnel}: Login through the mobile application with delivery credentials
    \item \textbf{Administrators}: Access through the web interface with admin privileges
\end{itemize}

\subsection{Key Functionalities}

\textbf{For Patients}:
\begin{itemize}
    \item Register and manage profile information
    \item Upload prescription images for OCR processing
    \item Track prescription status and delivery
    \item Receive notifications about prescription updates
\end{itemize}

\textbf{For Pharmacists}:
\begin{itemize}
    \item View and process patient prescriptions
    \item Create prescription packages (colis, colis+cueillette, cueillette)
    \item Manage patient profiles and prescriptions
    \item Send notifications to patients
\end{itemize}

\textbf{For Delivery Personnel}:
\begin{itemize}
    \item View assigned delivery tasks
    \item Update delivery status
    \item Navigate to delivery locations
\end{itemize}

This setup guide ensures that all stakeholders can successfully deploy and use the Med4Solutions digital health platform in their respective environments.
