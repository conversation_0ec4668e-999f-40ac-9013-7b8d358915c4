@startuml
!theme plain
skinparam backgroundColor white
skinparam participant {
    BackgroundColor lightblue
    BorderColor black
}
skinparam actor {
    BackgroundColor lightgreen
    BorderColor black
}

title Firebase Token Management Sequence Diagram

actor "User" as user
participant "iOS App" as mobile
participant "Web App" as web
participant "Firebase SDK\n(iOS)" as firebaseSDK
participant "Firebase SDK\n(Web)" as firebaseWebSDK
participant "Firebase Cloud\nMessaging" as fcm
participant "Backend API" as backend
participant "User Service" as userService
participant "MongoDB" as db

== iOS App Initialization ==
user -> mobile : Launch iOS application
mobile -> firebaseSDK : Initialize Firebase\nMessaging Service
firebaseSDK -> firebaseSDK : Request notification\npermissions

alt iOS Permission Granted
    == iOS Token Generation ==
    firebaseSDK -> fcm : Request FCM token
    fcm -> firebaseSDK : Return FCM token
    firebaseSDK -> mobile : Token generated successfully

    == iOS Token Registration ==
    mobile -> backend : PUT /notifications/fcm-token\n{\n  userId: "user123",\n  fcmToken: "token_string",\n  platform: "ios"\n}
    backend -> userService : updateFcmToken(userId, token)
    userService -> db : Update user.fcm = fcmToken
    db -> userService : Confirm token saved
    userService -> backend : Token registered successfully
    backend -> mobile : 200 OK\n{message: "Token registered"}
    mobile -> user : iOS app ready for notifications

== Web App Initialization ==
user -> web : Open web application
web -> firebaseWebSDK : Initialize Firebase\nMessaging Service
firebaseWebSDK -> firebaseWebSDK : Request notification\npermissions

alt Web Permission Granted
    == Web Token Generation ==
    firebaseWebSDK -> fcm : Request FCM token
    fcm -> firebaseWebSDK : Return FCM token
    firebaseWebSDK -> web : Token generated successfully

    == Web Token Registration ==
    web -> backend : PUT /notifications/fcm-token\n{\n  userId: "user123",\n  fcmToken: "web_token_string",\n  platform: "web"\n}
    backend -> userService : updateFcmToken(userId, token)
    userService -> db : Update user.fcm = fcmToken
    db -> userService : Confirm token saved
    userService -> backend : Token registered successfully
    backend -> web : 200 OK\n{message: "Token registered"}
    web -> user : Web app ready for notifications
    
    == iOS Token Refresh Handling ==
    note over firebaseSDK : FCM tokens can refresh\nperiodically or on app updates

    firebaseSDK -> firebaseSDK : onTokenRefresh listener\ntriggered
    firebaseSDK -> fcm : Request new FCM token
    fcm -> firebaseSDK : Return refreshed token
    firebaseSDK -> mobile : New token available

    mobile -> backend : PUT /notifications/fcm-token\n{\n  userId: "user123",\n  fcmToken: "new_ios_token"\n}
    backend -> userService : updateFcmToken(userId, newToken)
    userService -> db : Update user.fcm = newToken
    db -> userService : Confirm token updated
    userService -> backend : Token updated successfully
    backend -> mobile : 200 OK\n{message: "Token updated"}

else iOS Permission Denied
    firebaseSDK -> mobile : Permission denied
    mobile -> mobile : Store permission status\nDisable push notifications
    mobile -> user : Show notification settings\noption in app preferences
end

    == Web Token Refresh Handling ==
    note over firebaseWebSDK : Web FCM tokens can refresh\nwhen service worker updates

    firebaseWebSDK -> firebaseWebSDK : onTokenRefresh listener\ntriggered
    firebaseWebSDK -> fcm : Request new FCM token
    fcm -> firebaseWebSDK : Return refreshed token
    firebaseWebSDK -> web : New token available

    web -> backend : PUT /notifications/fcm-token\n{\n  userId: "user123",\n  fcmToken: "new_web_token"\n}
    backend -> userService : updateFcmToken(userId, newToken)
    userService -> db : Update user.fcm = newToken
    db -> userService : Confirm token updated
    userService -> backend : Token updated successfully
    backend -> web : 200 OK\n{message: "Token updated"}

else Web Permission Denied
    firebaseWebSDK -> web : Permission denied
    web -> web : Store permission status\nDisable push notifications
    web -> user : Show notification settings\noption in browser
end

== Token Validation (Backend) ==
note over backend : When sending notifications,\nbackend validates tokens

backend -> fcm : Validate FCM token\nbefore sending notification
alt Token Valid
    fcm -> backend : Token is valid
    backend -> backend : Proceed with\nnotification sending
else Token Invalid
    fcm -> backend : Token invalid/expired
    backend -> userService : markFcmTokenInvalid(userId)
    userService -> db : Set FCM token as invalid
    backend -> backend : Skip notification\nfor this user
end

== User Logout from iOS ==
user -> mobile : Logout from iOS application
mobile -> backend : POST /auth/logout\n{userId: "user123"}
backend -> userService : clearUserFcmToken(userId)
userService -> db : Remove FCM token\nfrom user document
db -> userService : Token removed
userService -> backend : Token cleared
backend -> mobile : 200 OK\n{message: "Logged out"}
mobile -> firebaseSDK : Clear local FCM token
firebaseSDK -> mobile : Token cleared locally

== User Logout from Web ==
user -> web : Logout from web application
web -> backend : POST /auth/logout\n{userId: "user123"}
backend -> userService : clearUserFcmToken(userId)
userService -> db : Remove FCM token\nfrom user document
db -> userService : Token removed
userService -> backend : Token cleared
backend -> web : 200 OK\n{message: "Logged out"}
web -> firebaseWebSDK : Clear local FCM token
firebaseWebSDK -> web : Token cleared locally

@enduml
