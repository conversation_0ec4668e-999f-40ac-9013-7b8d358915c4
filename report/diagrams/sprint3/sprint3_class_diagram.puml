@startuml sprint3_class_diagram
!theme plain
skinparam classAttributeIconSize 0
skinparam classFontSize 10
skinparam packageFontSize 12

package "Order Management" {
    class Order {
        +id: string
        +patientId: string
        +status: OrderStatus
        +orderDate: Date
        +totalAmount: number
        +productIds: string[]
        +flag: boolean
        +quantity: number
        +deliveryManNote?: string
        +createdAt: Date
        +updatedAt: Date
    }

    enum OrderStatus {
        PENDING
        APPROVED
        IN_TRANSIT
        DELIVERED
        CANCELLED
    }

    class OrderRepository {
        +create(order: Partial<Order>): Promise<Order>
        +update(id: string, update: Partial<Order>): Promise<Order>
        +delete(id: string): Promise<void>
        +findById(id: string): Promise<Order>
        +findBasketByPatientId(patientId: string): Promise<Order>
        +findAllByPatientId(patientId: string): Promise<Order[]>
        +findByFilters(filters: any): Promise<Order[]>
    }

    class OrderAddShop {
        +execute(command: OrderAddShopCommand): Promise<Order>
    }

    class OrderDeleteShop {
        +execute(patientId: string, productId: string): Promise<any>
    }

    class OrderGetBasket {
        +execute(patientId: string): Promise<Order>
    }

    class OrderConfirm {
        +execute(command: OrderConfirmCommand): Promise<Order>
    }

    class OrderMarkDelivered {
        +execute(orderId: string): Promise<Order>
    }

    class OrderGetHistory {
        +execute(patientId: string): Promise<Order[]>
    }

    class OrderController {
        +addToBasket(req: any, body: OrderAddShopCommand): Promise<any>
        +removeFromBasket(req: any, productId: string): Promise<any>
        +getBasket(req: any): Promise<any>
        +confirmOrder(req: any, body: OrderConfirmCommand): Promise<any>
        +markOrderAsDelivered(id: string, req: any): Promise<any>
        +getOrderHistory(req: any): Promise<any>
    }
}

package "Stock Management" {
    class Product {
        +id: string
        +pharmacyId: string
        +name: string
        +description: string
        +quantity: number
        +price: number
        +discount: number
        +storagePath: string[]
        +stockStatus: StockStatus
        +categoryId: string
        +expiryDate: Date
        +supplier: string
        +barcode: string
        +isTaxable: boolean
        +brand: string
        +actif: boolean
        +createdAt: Date
        +updatedAt: Date
    }

    enum StockStatus {
        IN_STOCK
        OUT_OF_STOCK
    }

    class Category {
        +id: string
        +name: string
        +createdAt: Date
        +updatedAt: Date
    }

    class ProductRepository {
        +create(product: Partial<Product>): Promise<Product>
        +update(id: string, update: Partial<Product>): Promise<Product>
        +delete(id: string): Promise<void>
        +findById(id: string): Promise<Product>
        +findAll(): Promise<Product[]>
        +findWithFilters(search?: string, categoryId?: string, minPrice?: number, maxPrice?: number): Promise<Product[]>
        +findForPharmacyWithFilters(pharmacyId: string, search?: string, stockStatus?: string, actif?: boolean): Promise<Product[]>
    }

    class CategoryRepository {
        +create(category: Partial<Category>): Promise<Category>
        +update(id: string, update: Partial<Category>): Promise<Category>
        +delete(id: string): Promise<void>
        +findById(id: string): Promise<Category>
        +findAll(): Promise<Category[]>
        +findByName(name: string): Promise<Category>
    }

    class ProductCreate {
        +execute(command: ProductCreateCommand, pharmacyId: string): Promise<Product>
    }

    class ProductUpdate {
        +execute(command: ProductUpdateCommand): Promise<Product>
    }

    class ProductDelete {
        +execute(id: string): Promise<void>
    }

    class ProductGetAll {
        +execute(command: ProductGetAllCommand): Promise<any>
    }

    class CategoryCreate {
        +execute(command: CategoryCreateCommand): Promise<Category>
    }

    class CategoryUpdate {
        +execute(command: CategoryUpdateCommand): Promise<Category>
    }

    class ProductController {
        +createProduct(req: any, body: ProductCreateCommand): Promise<any>
        +updateProduct(req: any, body: ProductUpdateCommand): Promise<any>
        +deleteProduct(id: string): Promise<any>
        +getAllProducts(query: ProductGetAllCommand): Promise<any>
        +getProductById(id: string): Promise<any>
    }

    class CategoryController {
        +createCategory(body: CategoryCreateCommand): Promise<any>
        +updateCategory(body: CategoryUpdateCommand): Promise<any>
        +deleteCategory(id: string): Promise<any>
        +getAllCategories(): Promise<any>
        +getCategoryById(id: string): Promise<any>
    }
}

' Relationships
Order ||--|| OrderStatus
Product ||--|| StockStatus
Product }|--|| Category

OrderRepository ||--o{ Order
ProductRepository ||--o{ Product
CategoryRepository ||--o{ Category

OrderController --> OrderAddShop
OrderController --> OrderDeleteShop
OrderController --> OrderGetBasket
OrderController --> OrderConfirm
OrderController --> OrderMarkDelivered
OrderController --> OrderGetHistory

OrderAddShop --> OrderRepository
OrderDeleteShop --> OrderRepository
OrderGetBasket --> OrderRepository
OrderConfirm --> OrderRepository
OrderConfirm --> ProductRepository
OrderMarkDelivered --> OrderRepository
OrderGetHistory --> OrderRepository

ProductController --> ProductCreate
ProductController --> ProductUpdate
ProductController --> ProductDelete
ProductController --> ProductGetAll

CategoryController --> CategoryCreate
CategoryController --> CategoryUpdate

ProductCreate --> ProductRepository
ProductUpdate --> ProductRepository
ProductDelete --> ProductRepository
ProductGetAll --> ProductRepository

CategoryCreate --> CategoryRepository
CategoryUpdate --> CategoryRepository

Order }|--o{ Product : contains

@enduml
